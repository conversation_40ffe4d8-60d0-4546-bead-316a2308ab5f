import { describe, it, expect, vi, beforeEach } from 'vitest';
import { projectSchema } from '$lib/schemas/project';
import { StandardRibaStages, StandardICMSStages } from '$lib/project_utils';

// Mock dependencies
vi.mock('$lib/server/auth', () => ({
	requireUser: vi.fn().mockResolvedValue({ user: { id: 'test-user-id' } }),
}));

vi.mock('sveltekit-superforms/server', () => ({
	superValidate: vi.fn(),
	message: vi.fn((form, message) => ({ form, message })),
}));

vi.mock('sveltekit-flash-message/server', () => ({
	redirect: vi.fn((url, message, cookies) => ({ url, message, cookies })),
}));

import { superValidate } from 'sveltekit-superforms/server';
import { actions } from '../../../routes/org/[org_name]/clients/[client_name]/projects/new/+page.server';

describe('Project Creation Server Actions', () => {
	let mockSupabase: any;
	let mockRequest: any;
	let mockLocals: any;
	let mockParams: any;
	let mockCookies: any;

	beforeEach(() => {
		vi.clearAllMocks();

		// Mock Supabase client
		mockSupabase = {
			from: vi.fn().mockImplementation((table) => ({
				select: vi.fn().mockReturnThis(),
				insert: vi.fn().mockReturnThis(),
				eq: vi.fn().mockReturnThis(),
				limit: vi.fn().mockReturnThis(),
				maybeSingle: vi.fn().mockResolvedValue({
					data: { client_id: 'test-client-id' },
					error: null,
				}),
			})),
		};

		mockLocals = { supabase: mockSupabase };
		mockParams = {
			org_name: 'test-org',
			client_name: 'test-client',
		};
		mockCookies = {};
		mockRequest = {
			formData: vi.fn(),
		};
	});

	describe('ICMS Stages Creation', () => {
		it('should create project with selected ICMS stages', async () => {
			const selectedStages = [0, 2, 4]; // Select stages 0, 2, and 4
			const formData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: 1,
				stage_selection_type: 'icms',
				selected_icms_stages: selectedStages,
				custom_stages: [],
			};

			// Mock superValidate to return valid form
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: formData,
				errors: {},
			} as any);

			// Mock successful project insertion
			mockSupabase.from.mockImplementation((table: string) => {
				if (table === 'client') {
					return {
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						limit: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: { client_id: 'test-client-id' },
							error: null,
						}),
					};
				}
				if (table === 'project') {
					return {
						insert: vi.fn().mockResolvedValue({
							data: [{ project_id: 'test-project-id' }],
							error: null,
						}),
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						limit: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: { project_id: 'test-project-id' },
							error: null,
						}),
					};
				}
				if (table === 'project_stage') {
					return {
						insert: vi.fn().mockImplementation((stages) => {
							// Verify only selected ICMS stages are being inserted
							expect(stages).toHaveLength(selectedStages.length);
							stages.forEach((stage: any, index: number) => {
								const originalStageIndex = selectedStages[index];
								expect(stage.name).toBe(StandardICMSStages[originalStageIndex].name);
								expect(stage.stage_order).toBe(index); // Reordered based on selection
								expect(stage.project_id).toBe('test-project-id');
							});
							return Promise.resolve({ error: null });
						}),
					};
				}
				return {};
			});

			const result = await actions.default({
				request: mockRequest,
				locals: mockLocals,
				params: mockParams,
				cookies: mockCookies,
			} as any);

			expect(result).toBeDefined();
		});
	});

	describe('RIBA Stages Creation', () => {
		it('should create project with selected RIBA stages', async () => {
			const selectedStages = [1, 3]; // Select stages 1 and 3
			const formData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: 1,
				stage_selection_type: 'riba',
				selected_riba_stages: selectedStages,
				custom_stages: [],
			};

			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: formData,
				errors: {},
			} as any);

			mockSupabase.from.mockImplementation((table: string) => {
				if (table === 'client') {
					return {
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						limit: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: { client_id: 'test-client-id' },
							error: null,
						}),
					};
				}
				if (table === 'project') {
					return {
						insert: vi.fn().mockResolvedValue({
							data: [{ project_id: 'test-project-id' }],
							error: null,
						}),
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						limit: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: { project_id: 'test-project-id' },
							error: null,
						}),
					};
				}
				if (table === 'project_stage') {
					return {
						insert: vi.fn().mockImplementation((stages) => {
							// Verify only selected RIBA stages are being inserted
							expect(stages).toHaveLength(selectedStages.length);
							stages.forEach((stage: any, index: number) => {
								const originalStageIndex = selectedStages[index];
								expect(stage.name).toBe(StandardRibaStages[originalStageIndex].name);
								expect(stage.stage_order).toBe(index); // Reordered based on selection
								expect(stage.project_id).toBe('test-project-id');
							});
							return Promise.resolve({ error: null });
						}),
					};
				}
				return {};
			});

			const result = await actions.default({
				request: mockRequest,
				locals: mockLocals,
				params: mockParams,
				cookies: mockCookies,
			} as any);

			expect(result).toBeDefined();
		});

		it('should create project with all ICMS stages selected', async () => {
			const allICMSStages = StandardICMSStages.map((_, index) => index);
			const formData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: 1,
				stage_selection_type: 'icms',
				selected_icms_stages: allICMSStages,
				custom_stages: [],
			};

			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: formData,
				errors: {},
			} as any);

			mockSupabase.from.mockImplementation((table: string) => {
				if (table === 'client') {
					return {
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						limit: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: { client_id: 'test-client-id' },
							error: null,
						}),
					};
				}
				if (table === 'project') {
					return {
						insert: vi.fn().mockResolvedValue({
							data: [{ project_id: 'test-project-id' }],
							error: null,
						}),
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						limit: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: { project_id: 'test-project-id' },
							error: null,
						}),
					};
				}
				if (table === 'project_stage') {
					return {
						insert: vi.fn().mockImplementation((stages) => {
							// Verify all ICMS stages are being inserted
							expect(stages).toHaveLength(StandardICMSStages.length);
							stages.forEach((stage: any, index: number) => {
								expect(stage.name).toBe(StandardICMSStages[index].name);
								expect(stage.stage_order).toBe(index);
								expect(stage.project_id).toBe('test-project-id');
							});
							return Promise.resolve({ error: null });
						}),
					};
				}
				return {};
			});

			const result = await actions.default({
				request: mockRequest,
				locals: mockLocals,
				params: mockParams,
				cookies: mockCookies,
			} as any);

			expect(result).toBeDefined();
		});
	});

	describe('Custom Stages Creation', () => {
		it('should create project with custom stages', async () => {
			const customStages = [
				{ name: 'Planning', description: 'Initial planning phase' },
				{ name: 'Design', description: 'Design phase' },
				{ name: 'Construction', description: null },
			];

			const formData = {
				name: 'Test Project',
				description: 'Test description',
				import_from_costx: false,
				wbs_library_id: 1,
				stage_selection_type: 'custom',
				custom_stages: customStages,
			};

			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: true,
				data: formData,
				errors: {},
			} as any);

			mockSupabase.from.mockImplementation((table: string) => {
				if (table === 'client') {
					return {
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						limit: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: { client_id: 'test-client-id' },
							error: null,
						}),
					};
				}
				if (table === 'project') {
					return {
						insert: vi.fn().mockResolvedValue({
							data: [{ project_id: 'test-project-id' }],
							error: null,
						}),
						select: vi.fn().mockReturnThis(),
						eq: vi.fn().mockReturnThis(),
						limit: vi.fn().mockReturnThis(),
						maybeSingle: vi.fn().mockResolvedValue({
							data: { project_id: 'test-project-id' },
							error: null,
						}),
					};
				}
				if (table === 'project_stage') {
					return {
						insert: vi.fn().mockImplementation((stages) => {
							// Verify custom stages are being inserted
							expect(stages).toHaveLength(customStages.length);
							stages.forEach((stage: any, index: number) => {
								expect(stage.name).toBe(customStages[index].name);
								expect(stage.stage_order).toBe(index);
								expect(stage.description).toBe(customStages[index].description || undefined);
								expect(stage.project_id).toBe('test-project-id');
							});
							return Promise.resolve({ error: null });
						}),
					};
				}
				return {};
			});

			const result = await actions.default({
				request: mockRequest,
				locals: mockLocals,
				params: mockParams,
				cookies: mockCookies,
			} as any);

			expect(result).toBeDefined();
		});
	});

	describe('Form Validation', () => {
		it('should return error for invalid form data', async () => {
			vi.mocked(superValidate).mockResolvedValueOnce({
				valid: false,
				data: {},
				errors: { name: ['Project name is required'] },
			} as any);

			const result = await actions.default({
				request: mockRequest,
				locals: mockLocals,
				params: mockParams,
				cookies: mockCookies,
			} as any);

			// Check that it's an ActionFailure with status 400
			expect(result).toHaveProperty('status', 400);
			expect(result).toHaveProperty('data');
			expect((result as any).data.form.valid).toBe(false);
			expect((result as any).data.form.errors.name).toEqual(['Project name is required']);
		});
	});
});
