import { z } from 'zod';

export const projectRoles = ['viewer', 'editor', 'owner'] as const;

// Stage selection types
export const stageSelectionTypes = ['icms', 'riba', 'custom'] as const;

// Custom stage schema
export const customStageSchema = z.object({
	name: z.string().min(1, 'Stage name is required'),
	description: z.string().optional().nullable(),
});

export const projectSchema = z.discriminatedUnion('stage_selection_type', [
	z.object({
		name: z.string().min(1, 'Project name is required'),
		description: z.string().optional().nullable(),
		import_from_costx: z.boolean().default(false),
		wbs_library_id: z
			.number({
				required_error: 'Please select a WBS library',
			})
			.positive('Please select a WBS library'),
		stage_selection_type: z.literal(stageSelectionTypes[0]),
		selected_icms_stages: z.array(z.number()).min(1, 'At least one ICMS stage is required'),
		selected_riba_stages: z.array(z.number()).optional().default([]),
		custom_stages: z
			.array(z.object({ name: z.string() }))
			.optional()
			.default([]),
	}),
	z.object({
		name: z.string().min(1, 'Project name is required'),
		description: z.string().optional().nullable(),
		import_from_costx: z.boolean().default(false),
		wbs_library_id: z
			.number({
				required_error: 'Please select a WBS library',
			})
			.positive('Please select a WBS library'),
		stage_selection_type: z.literal(stageSelectionTypes[1]),
		selected_icms_stages: z.array(z.number()).optional().default([]),
		selected_riba_stages: z.array(z.number()).min(1, 'At least one RIBA stage is required'),
		custom_stages: z
			.array(z.object({ name: z.string() }))
			.optional()
			.default([]),
	}),
	z.object({
		name: z.string().min(1, 'Project name is required'),
		description: z.string().optional().nullable(),
		import_from_costx: z.boolean().default(false),
		wbs_library_id: z
			.number({
				required_error: 'Please select a WBS library',
			})
			.positive('Please select a WBS library'),
		stage_selection_type: z.literal(stageSelectionTypes[2]),
		selected_icms_stages: z.array(z.number()).optional().default([]),
		selected_riba_stages: z.array(z.number()).optional().default([]),
		custom_stages: z.array(customStageSchema).min(1, 'At least one custom stage is required'),
	}),
]);

export type ProjectSchema = typeof projectSchema;

export const projectInviteSchema = z.object({
	email: z.string().email('Please enter a valid email address').min(1, 'Email is required'),
	role: z
		.enum(projectRoles, {
			required_error: 'Please select a role',
		})
		.default('viewer'),
});

export type ProjectInviteForm = z.infer<typeof projectInviteSchema>;

// Budget item form schema
export const budgetItemSchema = z.object({
	budget_line_item_id: z.number().optional(),
	project_id: z.string().uuid(),
	wbs_library_item_id: z.string().uuid(),
	quantity: z.number(),
	unit: z.string().optional().nullable(),
	material_rate: z.number(),
	labor_rate: z.number().optional().nullable(),
	productivity_per_hour: z.number().optional().nullable(),
	unit_rate_manual_override: z.boolean().default(false),
	unit_rate: z.number().optional(),
	factor: z.number().optional().nullable(),
	cost_certainty: z.number().optional().nullable(),
	design_certainty: z.number().optional().nullable(),
	remarks: z.string().optional().nullable(),
});

// Type for creating a new budget item (no ID)
export type CreateBudgetItem = Omit<z.infer<typeof budgetItemSchema>, 'budget_line_item_id'> & {
	budget_line_item_id?: undefined;
};

// Type for updating an existing budget item (requires ID)
export type UpdateBudgetItem = z.infer<typeof budgetItemSchema> & {
	budget_line_item_id: number;
};

// Combined type for either creating or updating
export type BudgetItem = CreateBudgetItem | UpdateBudgetItem;
